import sys
import os
import importlib
import types
import pytest

# test_settings.py


# Ensure settings module is reloaded for each test
@pytest.fixture
def settings_module(monkeypatch):
    # Remove nederlearn.settings from sys.modules to force reload
    if "nederlearn.settings" in sys.modules:
        del sys.modules["nederlearn.settings"]
    # Patch sys.argv for test database switching
    monkeypatch.setattr(sys, "argv", ["manage.py", "test"])
    settings = importlib.import_module("nederlearn.settings")
    return settings

def test_debug_is_true(settings_module):
    assert settings_module.DEBUG is True

def test_allowed_hosts(settings_module):
    hosts = settings_module.ALLOWED_HOSTS
    assert "localhost" in hosts
    assert "127.0.0.1" in hosts
    assert any("herokuapp.com" in h for h in hosts)

def test_installed_apps(settings_module):
    apps = settings_module.INSTALLED_APPS
    assert "django.contrib.admin" in apps
    assert "allauth" in apps
    assert "blog.apps.BlogConfig" in apps

def test_database_switches_to_sqlite(settings_module):
    db = settings_module.DATABASES["default"]
    assert db["ENGINE"] == "django.db.backends.sqlite3"
    assert str(db["NAME"]).endswith("db.sqlite3")

def test_message_tags(settings_module):
    tags = settings_module.MESSAGE_TAGS
    assert tags[settings_module.messages.DEBUG] == "alert-info"
    assert tags[settings_module.messages.SUCCESS] == "alert-success"
    assert tags[settings_module.messages.ERROR] == "alert-danger"

def test_summernote_config_width(settings_module):
    config = settings_module.SUMMERNOTE_CONFIG
    assert config["summernote"]["width"] == "100%"

def test_staticfiles_dirs_and_root(settings_module):
    dirs = settings_module.STATICFILES_DIRS
    root = settings_module.STATIC_ROOT
    assert any("static" in str(d) for d in dirs)
    assert "staticfiles" in str(root)

def test_cloudinary_storage_settings(settings_module):
    assert (
        settings_module.DEFAULT_FILE_STORAGE
        == "cloudinary_storage.storage.MediaCloudinaryStorage"
    )
    assert (
        settings_module.STATICFILES_STORAGE
        == "cloudinary_storage.storage.StaticHashedCloudinaryStorage"
    )

def test_account_email_verification(settings_module):
    assert settings_module.ACCOUNT_EMAIL_VERIFICATION == "none"

def test_session_expire_at_browser_close(settings_module):
    assert settings_module.SESSION_EXPIRE_AT_BROWSER_CLOSE is True